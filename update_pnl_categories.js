#!/usr/bin/env node
/**
 * Update existing P&L lines with category information
 * This script adds categories to existing pnl_lines records
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

/**
 * Get the category name for a given account name
 * @param {string} accountName - The account name to categorize
 * @returns {string|null} - The category name or null if not categorized
 */
function getCategoryForAccount(accountName) {
  if (!accountName) return null;

  // Operating Expenses
  if (
    [
      "Cleaning - office",
      "Clinical Tool Licensure",
      "Equipment Maint. & Repair",
      "Legal & Accounting",
      "Medical Supplies",
      "Patient Notification Tools",
      "Office Expenses",
      "Practice Mgmt Software Lease",
      "Rent",
      "Telephone Office",
      "Utilities",
      "Vaccines",
      "Website Expense",
      "Workmans Comp Ins",
    ].includes(accountName)
  ) {
    return "Operating Expenses";
  }

  // Admin
  if (
    [
      "Doctor Admin Income Expense - Canner",
      "Doctor Admin Income Expense - Ghosh",
      "Doctor Admin Income Expense - Pierce",
      "Doctor Admin Income Expense - Scherer",
      "Doctor Admin Income Expense - Shah",
    ].includes(accountName)
  ) {
    return "Admin";
  }

  // Dues, Subscriptions & License
  if (
    [
      "Dues, Sub & Lic Deb Ghosh",
      "Dues, Subs & Lic - Dr. Canner",
      "Dues, Subs & Lic - Dr. George",
      "Dues, Subs & Lic - Dr. Pierce",
      "Dues, Subs & Lic - Dr. Scherer",
      "Dues, Subs & Lic - Dr. Shah",
      "Dues, Subs & Lic - Dr. Shaikh",
    ].includes(accountName)
  ) {
    return "Dues, Subscriptions & License";
  }

  // Payroll Tax Expense (includes both Payroll Expenses and Salary Expense)
  if (
    [
      "Payroll Tax - Associate Dr. Shaikh",
      "Payroll Tax - Dr. Canner",
      "Payroll Tax - Dr. Ghosh",
      "Payroll Tax - Dr. Pierce",
      "Payroll Tax - Dr. Scherer",
      "Payroll Tax - Dr. Shah",
      "Payroll Tax - Office Staff",
      "Payroll - Office Staff",
      "Salary - Associate Dr. Shaikh",
      "Salary - Dr. Canner",
      "Salary - Dr. Ghosh",
      "Salary - Dr. Pierce",
      "Salary - Dr. Scherer",
      "Salary - Dr. Shah",
    ].includes(accountName)
  ) {
    return "Payroll Tax Expense";
  }

  // Other Expenses
  if (
    [
      "Bank Charge",
      "Meals & Entertainment",
      "Profit Sharing",
      "Refunds",
      "Reimbursement",
      "Service fee",
      "Interest Expense",
    ].includes(accountName)
  ) {
    return "Other Expenses";
  }

  // Health Insurance Expense (Medical)
  if (
    [
      "Health Insurance - B. Scherer",
      "Health Insurance - Employee",
      "Health Insurance - J. Canner",
    ].includes(accountName)
  ) {
    return "Health Insurance Expense (Medical)";
  }

  return null; // No category found
}

async function updatePnLCategories() {
  await pg.connect();
  
  console.log("Updating P&L line categories...");
  
  // Get all distinct account names
  const { rows: accounts } = await pg.query(`
    SELECT DISTINCT account_name 
    FROM pnl_lines 
    WHERE account_name IS NOT NULL 
    ORDER BY account_name
  `);
  
  console.log(`Found ${accounts.length} unique account names`);
  
  let updatedCount = 0;
  let categorizedCount = 0;
  
  // Update each account name with its category
  for (const account of accounts) {
    const category = getCategoryForAccount(account.account_name);
    
    if (category) {
      const { rowCount } = await pg.query(`
        UPDATE pnl_lines 
        SET category = $1 
        WHERE account_name = $2
      `, [category, account.account_name]);
      
      updatedCount += rowCount;
      categorizedCount++;
      console.log(`  ✅ ${account.account_name} -> ${category} (${rowCount} rows)`);
    } else {
      console.log(`  ⚠️  ${account.account_name} -> No category found`);
    }
  }
  
  console.log("");
  console.log(`✅ Update complete:`);
  console.log(`   Accounts with categories: ${categorizedCount}/${accounts.length}`);
  console.log(`   Total rows updated: ${updatedCount}`);
  
  // Show sample results
  const { rows: samples } = await pg.query(`
    SELECT account_name, category, COUNT(*) as count
    FROM pnl_lines 
    WHERE category IS NOT NULL
    GROUP BY account_name, category
    ORDER BY category, account_name
    LIMIT 15
  `);
  
  console.log("");
  console.log("Sample categorized accounts:");
  let currentCategory = "";
  samples.forEach(row => {
    if (row.category !== currentCategory) {
      currentCategory = row.category;
      console.log(`\n  ${currentCategory}:`);
    }
    console.log(`    ${row.account_name} (${row.count} entries)`);
  });
  
  await pg.end();
}

updatePnLCategories().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
